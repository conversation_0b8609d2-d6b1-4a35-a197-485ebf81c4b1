package com.isxcode.spark.plugin.real.sync;

import com.alibaba.fastjson.JSON;
import com.isxcode.spark.api.agent.req.PluginReq;
import com.isxcode.spark.api.datasource.constants.DatasourceType;
import com.isxcode.spark.api.func.constants.FuncType;
import com.isxcode.spark.api.work.dto.SyncColumnInfo;
import com.isxcode.spark.api.work.dto.SyncColumnMap;
import com.isxcode.spark.plugin.real.sync.function.DebeziumParse;
import com.isxcode.spark.plugin.real.sync.function.JsonParse;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.logging.log4j.util.Strings;
import org.apache.spark.SparkConf;
import org.apache.spark.sql.*;
import org.apache.spark.sql.streaming.StreamingQueryException;
import org.apache.spark.sql.streaming.Trigger;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.MapType;
import scala.concurrent.duration.Duration;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

public class Execute {

    public static void main(String[] args) throws StreamingQueryException, TimeoutException {

        PluginReq pluginReq = parse(args);

        // 只读取有连线的数据
        List<String> sourceColIdList = pluginReq.getSyncWorkConfig().getColumnMap().stream()
            .map(SyncColumnMap::getSource).collect(Collectors.toList());
        List<String> tagetColIdList = pluginReq.getSyncWorkConfig().getColumnMap().stream()
            .map(SyncColumnMap::getTarget).collect(Collectors.toList());
        Map<String, String> columnMapping = new HashMap<>();
        pluginReq.getSyncWorkConfig().getColumnMap().forEach(e -> {
            columnMapping.put(e.getSource(), e.getTarget());
        });
        List<SyncColumnInfo> sourceColumn = pluginReq.getSyncWorkConfig().getSourceTableColumn().stream()
            .filter(e -> sourceColIdList.contains(e.getCode())).collect(Collectors.toList());
        HashMap<String, SyncColumnInfo> sourceColMap = new HashMap<>();
        sourceColumn.forEach(e -> sourceColMap.put(e.getCode(), e));

        // 留个spark的对象.属性值
        List<String> sourceColumnCode =
            sourceColumn.stream().map(e -> "data." + e.getCode()).collect(Collectors.toList());
        sourceColumnCode.add("data.successFlag");

        // 翻译参数,和自定义函数
        AtomicReference<String> sourceColStr = new AtomicReference<>("");
        sourceColIdList.forEach(e -> {
            SyncColumnInfo syncColumnInfo = sourceColMap.get(e);
            if (syncColumnInfo.getSql() == null || syncColumnInfo.getSql().isEmpty()) {
                sourceColStr.set(
                    sourceColStr + translateDataType(e, syncColumnInfo) + " as " + syncColumnInfo.getCode() + " ,");
            } else {
                sourceColStr.set(sourceColStr + syncColumnInfo.getSql() + " as " + syncColumnInfo.getCode() + " ,");
            }

        });
        AtomicReference<String> targetColStr = new AtomicReference<>("");
        tagetColIdList.forEach(e -> targetColStr.set(targetColStr + e + ","));

        // 去除左右一个逗号
        String sourceSubstring = sourceColStr.get().substring(0, sourceColStr.get().length() - 1);
        String targetSubstring = targetColStr.get().substring(0, targetColStr.get().length() - 1);

        // 初始化sparkSession
        try (SparkSession sparkSession = initSparkSession(pluginReq)) {

            // 注册自定义函数
            if (pluginReq.getFuncInfoList() != null) {
                pluginReq.getFuncInfoList().forEach(e -> {
                    if (FuncType.UDF.equals(e.getType())) {
                        sparkSession.udf().registerJava(e.getFuncName(), e.getClassName(),
                            getResultType(e.getResultType()));
                    } else if (FuncType.UDAF.equals(e.getType())) {
                        sparkSession.udf().registerJavaUDAF(e.getFuncName(), e.getClassName());
                    }
                });
            }

            // 注册json解析函数
            sparkSession.udf().register("JsonParse", new JsonParse(sourceColumn),
                new MapType(DataTypes.StringType, DataTypes.StringType, true));

            // 连接mysql，转成临时表target_table
            String sourceTableName;
            if (DatasourceType.HIVE.equals(pluginReq.getSyncWorkConfig().getTargetDBType())) {
                sourceTableName = pluginReq.getDatabase() + "." + pluginReq.getSyncWorkConfig().getTargetTable();
            } else {
                DataFrameReader frameReader = sparkSession.read().format("jdbc")
                    .option("driver", pluginReq.getSyncWorkConfig().getTargetDatabase().getDriver())
                    .option("url", pluginReq.getSyncWorkConfig().getTargetDatabase().getUrl())
                    .option("dbtable", pluginReq.getSyncWorkConfig().getTargetDatabase().getDbTable())
                    .option("user", pluginReq.getSyncWorkConfig().getTargetDatabase().getUser());

                if (pluginReq.getSyncWorkConfig().getTargetDatabase().getPassword() != null) {
                    frameReader.option("password", pluginReq.getSyncWorkConfig().getTargetDatabase().getPassword());
                }

                Dataset<Row> source = frameReader.load();
                source.createOrReplaceTempView("target_table");
                sourceTableName = "target_table";
            }

            // 如果是数据实时同步的话
            HikariDataSource dataSource;
            QueryRunner qr;
            if (!DatasourceType.KAFKA.equals(pluginReq.getSyncWorkConfig().getSourceDBType())) {

                // 添加简单连接池
                HikariConfig config = new HikariConfig();
                config.setJdbcUrl(pluginReq.getSyncWorkConfig().getTargetDatabase().getUrl());
                config.setUsername(pluginReq.getSyncWorkConfig().getTargetDatabase().getUser());
                if (pluginReq.getSyncWorkConfig().getTargetDatabase().getPassword() != null) {
                    config.setPassword(pluginReq.getSyncWorkConfig().getTargetDatabase().getPassword());
                }

                config.setMaximumPoolSize(2);
                config.setConnectionTimeout(40000);
                config.setIdleTimeout(600000);
                config.setMaxLifetime(1800000);
                config.setLeakDetectionThreshold(2000);

                dataSource = new HikariDataSource(config);
                qr = new QueryRunner();

                // 解析来源和去向
                Map<String, String> sourceTableColumnMap = new HashMap<>();
                pluginReq.getSyncWorkConfig().getSourceTableColumn()
                    .forEach(e -> sourceTableColumnMap.put(e.getCode(), e.getType()));
                Map<String, String> targetTableColumnMap = new HashMap<>();
                pluginReq.getSyncWorkConfig().getTargetTableColumn()
                    .forEach(e -> targetTableColumnMap.put(e.getCode(), e.getType()));

                sparkSession.udf().register("DebeziumParse",
                    new DebeziumParse(pluginReq.getSyncWorkConfig().getTargetDatabase().getDbTable(),
                        pluginReq.getSyncWorkConfig().getCat(), sourceTableColumnMap, targetTableColumnMap,
                        columnMapping),
                    DataTypes.StringType);
            } else {
                qr = null;
                dataSource = null;
            }

            // 连接kafka
            Dataset<Row> directStream = sparkSession.readStream().format("kafka")
                .option("kafka.bootstrap.servers", pluginReq.getSyncWorkConfig().getKafkaConfig().getBootstrapServers())
                .option("subscribe", pluginReq.getSyncWorkConfig().getKafkaConfig().getTopic())
                .option("startingOffsets", pluginReq.getSyncWorkConfig().getKafkaConfig().getStartingOffsets()).load();

            // 开始实时处理
            sparkSession.streams().addListener(new RealWorkQueryListener());

            // 转换kafka的数据
            directStream.selectExpr("CAST(value AS STRING)").as(Encoders.STRING()).writeStream()
                .foreachBatch((batch, batchId) -> {
                    if (batch.count() > 0) {
                        if (DatasourceType.KAFKA.equals(pluginReq.getSyncWorkConfig().getSourceDBType())) {
                            // 使用jsonParse函数，把数据转成表
                            Dataset<Row> data =
                                batch.withColumn("data", functions.callUDF("JsonParse", batch.col("value")))
                                    .selectExpr(sourceColumnCode.toArray(new String[0]));
                            data.createOrReplaceTempView("source_table");

                            // 组装sql
                            if (pluginReq.getSyncWorkConfig().getQueryCondition() == null
                                || pluginReq.getSyncWorkConfig().getQueryCondition().isEmpty()) {
                                data.sparkSession()
                                    .sql("insert into " + sourceTableName + " ( " + targetSubstring + " ) select "
                                        + sourceSubstring + " from source_table where successFlag = 'success' ");
                            } else {
                                data.sparkSession()
                                    .sql("insert into " + sourceTableName + " ( " + targetSubstring + " ) select "
                                        + sourceSubstring + " from source_table where successFlag = 'success' and ( "
                                        + pluginReq.getSyncWorkConfig().getQueryCondition() + " )");
                            }
                        } else {
                            // 使用jsonParse函数，把数据转成表
                            Dataset<Row> data =
                                batch.withColumn("data", functions.callUDF("DebeziumParse", batch.col("value")));
                            Connection connection = dataSource.getConnection();
                            data.collectAsList().forEach(e -> {
                                String sql = e.getString(1);
                                if (Strings.isNotEmpty(sql)) {
                                    try {
                                        qr.execute(connection, sql);
                                    } catch (SQLException ignore) {
                                    }
                                }
                            });
                            connection.close();
                        }
                    }
                })
                .trigger(Trigger
                    .ProcessingTime(Duration.apply(pluginReq.getSyncWorkConfig().getKafkaConfig().getDurationTime())))
                .start().awaitTermination();
        }
    }

    public static PluginReq parse(String[] args) {
        if (args.length == 0) {
            throw new RuntimeException("args is empty");
        }
        return JSON.parseObject(Base64.getDecoder().decode(args[0]), PluginReq.class);
    }

    public static SparkSession initSparkSession(PluginReq pluginReq) {

        SparkSession.Builder sparkSessionBuilder = SparkSession.builder();

        SparkConf conf = new SparkConf();
        if (pluginReq.getSparkConfig() != null) {
            for (Map.Entry<String, String> entry : pluginReq.getSparkConfig().entrySet()) {
                conf.set(entry.getKey(), entry.getValue());
            }
        }
        return sparkSessionBuilder.config(conf).enableHiveSupport().getOrCreate();
    }

    public static String translateDataType(String colName, SyncColumnInfo columnInfo) {

        switch (columnInfo.getType()) {
            case "int":
                return "int(" + colName + ")";
            case "bigDecimal":
                return "decimal(" + colName + ")";
            case "boolean":
                return "boolean(" + colName + ")";
            default:
                return colName;
        }
    }

    private static DataType getResultType(String resultType) {
        switch (resultType) {
            case "string":
                return DataTypes.StringType;
            case "int":
                return DataTypes.IntegerType;
            case "long":
                return DataTypes.LongType;
            case "double":
                return DataTypes.DoubleType;
            case "boolean":
                return DataTypes.BooleanType;
            case "date":
                return DataTypes.DateType;
            case "timestamp":
                return DataTypes.TimestampType;
            default:
                return DataTypes.StringType;
        }
    }
}
