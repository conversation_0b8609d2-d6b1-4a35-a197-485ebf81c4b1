package com.isxcode.spark.plugin.real.sync.function;

import com.alibaba.fastjson2.JSON;
import com.isxcode.spark.plugin.real.sync.debezium.DebeziumBody;
import org.apache.logging.log4j.util.Strings;
import org.apache.spark.sql.api.java.UDF1;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * json解析数据.
 */
public class DebeziumParse implements UDF1<String, String> {

    private final String targetTableName;

    private final List<String> cat;

    private final Map<String, String> sourceTableColumnMap;

    private final Map<String, String> targetTableColumnMap;

    private final Map<String, String> columnMapping;

    public DebeziumParse(String targetTableName, List<String> cat, Map<String, String> sourceTableColumnMap,
        Map<String, String> targetTableColumnMap, Map<String, String> columnMapping) {
        this.targetTableName = targetTableName;
        this.cat = cat;
        this.sourceTableColumnMap = sourceTableColumnMap;
        this.targetTableColumnMap = targetTableColumnMap;
        this.columnMapping = columnMapping;
    }

    @Override
    public String call(String json) {

        if (json == null) {
            return "";
        }

        if (Strings.isNotEmpty(json)) {
            DebeziumBody debezium = JSON.parseObject(json, DebeziumBody.class);
            if ("c".equals(debezium.getPayload().getOp()) && cat.contains("c")) {
                // 插入
                List<String> colList = new ArrayList<>();
                List<Object> valList = new ArrayList<>();
                debezium.getPayload().getAfter().forEach((k, v) -> {
                    colList.add(columnMapping.get(k));
                    if (v == null) {
                        valList.add(null);
                    } else {
                        String type = sourceTableColumnMap.get(k);
                        if (type.toLowerCase().contains("int") || type.toLowerCase().contains("long")
                            || type.toLowerCase().contains("bool") || type.toLowerCase().contains("double")) {
                            valList.add(v);
                        } else {
                            valList.add("'" + v + "'");
                        }
                    }
                });

                return "INSERT INTO " + targetTableName + " (" + Strings.join(colList, ',') + ") VALUES ("
                    + Strings.join(valList, ',') + ")";
            } else if ("d".equals(debezium.getPayload().getOp()) && cat.contains("d")) {
                // 删除
                List<String> colList = new ArrayList<>();
                List<Object> valList = new ArrayList<>();
                debezium.getPayload().getBefore().forEach((k, v) -> {
                    colList.add(columnMapping.get(k));
                    if (v == null) {
                        valList.add(columnMapping.get(k) + " is null");
                    } else {
                        String type = sourceTableColumnMap.get(k);
                        if (type.toLowerCase().contains("int") || type.toLowerCase().contains("long")
                            || type.toLowerCase().contains("bool") || type.toLowerCase().contains("double")) {
                            valList.add(columnMapping.get(k) + " = " + v);
                        } else {
                            valList.add(columnMapping.get(k) + " = '" + v + "'");
                        }
                    }
                });
                StringBuilder valListStr = new StringBuilder();
                for (int i = 0; i < valList.size(); i++) {
                    valListStr.append(valList.get(i));
                    if (i < valList.size() - 1) {
                        valListStr.append(" and ");
                    }
                }
                return "DELETE FROM " + targetTableName + " WHERE " + valListStr;
            } else if ("u".equals(debezium.getPayload().getOp()) && cat.contains("u")) {
                // 更新
                List<String> beforeValue = new ArrayList<>();
                debezium.getPayload().getBefore().forEach((k, v) -> {
                    if (v == null) {
                        beforeValue.add(columnMapping.get(k) + " is null");
                    } else {
                        String type = sourceTableColumnMap.get(k);
                        if (type.toLowerCase().contains("int") || type.toLowerCase().contains("long")
                            || type.toLowerCase().contains("bool") || type.toLowerCase().contains("double")) {
                            beforeValue.add(columnMapping.get(k) + " = " + v);
                        } else {
                            beforeValue.add(columnMapping.get(k) + " = '" + v + "'");
                        }
                    }
                });

                List<String> afterValue = new ArrayList<>();
                debezium.getPayload().getAfter().forEach((k, v) -> {
                    if (v == null) {
                        afterValue.add(columnMapping.get(k) + " is null");
                    } else {
                        String type = targetTableColumnMap.get(columnMapping.get(k));
                        if (type.toLowerCase().contains("int") || type.toLowerCase().contains("long")
                            || type.toLowerCase().contains("bool") || type.toLowerCase().contains("double")) {
                            afterValue.add(columnMapping.get(k) + " = " + v);
                        } else {
                            afterValue.add(columnMapping.get(k) + " = '" + v + "'");
                        }
                    }
                });

                StringBuilder beforeValueStr = new StringBuilder();
                for (int i = 0; i < beforeValue.size(); i++) {
                    beforeValueStr.append(beforeValue.get(i));
                    if (i < beforeValue.size() - 1) {
                        beforeValueStr.append(" and ");
                    }
                }
                return "UPDATE " + targetTableName + " SET " + Strings.join(afterValue, ',') + " WHERE "
                    + beforeValueStr;
            } else {
                return "";
            }
        } else {
            return "";
        }
    }
}
