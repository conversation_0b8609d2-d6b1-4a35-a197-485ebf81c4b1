package com.isxcode.spark.plugin.real.sync.function;

import com.alibaba.fastjson2.JSONPath;
import com.isxcode.spark.api.work.dto.SyncColumnInfo;
import org.apache.spark.sql.api.java.UDF1;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * json解析数据.
 */
public class JsonParse implements UDF1<String, Map<String, String>> {

    private final List<SyncColumnInfo> columnInfoList;

    public JsonParse(List<SyncColumnInfo> columnInfoList) {
        this.columnInfoList = columnInfoList;
    }

    @Override
    public Map<String, String> call(String json) {

        Map<String, String> result = new HashMap<>();
        for (SyncColumnInfo syncColumnInfo : columnInfoList) {
            String value = String.valueOf(JSONPath.eval(json, syncColumnInfo.getJsonPath()));
            result.put(syncColumnInfo.getCode(), value);
        }
        result.put("successFlag", "success");
        return result;
    }
}
