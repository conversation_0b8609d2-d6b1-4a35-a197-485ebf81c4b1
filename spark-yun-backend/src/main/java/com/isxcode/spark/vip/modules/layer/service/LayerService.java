package com.isxcode.spark.vip.modules.layer.service;

import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.layer.entity.LayerEntity;
import com.isxcode.spark.modules.layer.repository.LayerRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class LayerService {

    private final LayerRepository layerRepository;

    public LayerEntity getLayer(String layerId) {

        return layerRepository.findById(layerId).orElseThrow(() -> new IsxAppException("数据分层不存在"));
    }

    public String getLayerName(String layerId) {

        LayerEntity layer = layerRepository.findById(layerId).orElse(null);
        return layer == null ? layerId : layer.getName();
    }

    public String getLayerFullPathName(String layerId) {

        LayerEntity layer = layerRepository.findById(layerId).orElse(null);
        return layer == null ? layerId
            : layer.getParentNameList() == null ? layer.getName() : layer.getParentNameList() + "." + layer.getName();
    }
}
