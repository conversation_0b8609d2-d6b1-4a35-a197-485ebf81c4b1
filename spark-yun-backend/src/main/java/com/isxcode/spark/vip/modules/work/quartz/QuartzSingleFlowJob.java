package com.isxcode.spark.vip.modules.work.quartz;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.isxcode.spark.api.instance.constants.FlowInstanceStatus;
import com.isxcode.spark.api.instance.constants.InstanceStatus;
import com.isxcode.spark.api.instance.constants.InstanceType;
import com.isxcode.spark.api.work.dto.CronConfig;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.work.entity.VipWorkVersionEntity;
import com.isxcode.spark.modules.work.entity.WorkEntity;
import com.isxcode.spark.modules.work.entity.WorkInstanceEntity;
import com.isxcode.spark.modules.work.repository.VipWorkVersionRepository;
import com.isxcode.spark.modules.work.repository.WorkInstanceRepository;
import com.isxcode.spark.modules.work.repository.WorkRepository;
import com.isxcode.spark.modules.workflow.entity.WorkflowEntity;
import com.isxcode.spark.modules.workflow.entity.WorkflowInstanceEntity;
import com.isxcode.spark.modules.workflow.entity.WorkflowVersionEntity;
import com.isxcode.spark.modules.workflow.repository.WorkflowInstanceRepository;
import com.isxcode.spark.modules.workflow.repository.WorkflowRepository;
import com.isxcode.spark.modules.workflow.repository.WorkflowVersionRepository;
import com.isxcode.spark.modules.workflow.run.WorkflowRunEvent;
import com.isxcode.spark.modules.workflow.service.WorkflowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.isxcode.spark.common.config.CommonConfig.TENANT_ID;
import static com.isxcode.spark.common.config.CommonConfig.USER_ID;

@Slf4j
@Component
@RequiredArgsConstructor
public class QuartzSingleFlowJob implements Job {

    private final WorkflowVersionRepository workflowVersionRepository;

    private final WorkflowInstanceRepository workflowInstanceRepository;

    private final WorkInstanceRepository workInstanceRepository;

    private final Scheduler scheduler;

    private final VipWorkVersionRepository vipWorkVersionRepository;

    private final WorkRepository workRepository;

    private final WorkflowService workflowService;

    private final WorkflowRepository workflowRepository;

    @Override
    public void execute(JobExecutionContext context) {

        // 从context中获取作业流的运行参数
        QuartzFlowJobContext quartzFlowJobRunContext =
            JSON.parseObject(String.valueOf(context.getJobDetail().getJobDataMap().get("quartzFlowJobRunContext")),
                QuartzFlowJobContext.class);

        // 初始化异步线程中的上下文
        USER_ID.set(quartzFlowJobRunContext.getUserId());
        TENANT_ID.set(quartzFlowJobRunContext.getTenantId());

        // 获取工作流的版本信息
        WorkflowVersionEntity workflowVersion =
            workflowVersionRepository.findById(quartzFlowJobRunContext.getFlowVersionId()).get();

        // 生效时间限制，如果超出时间，直接跳过不生成实例
        CronConfig cronConfig = JSON.parseObject(workflowVersion.getCronConfig(), CronConfig.class);
        if (cronConfig.getWorkDate() != null && cronConfig.getWorkDate().size() == 2) {
            if (LocalDate.now().isAfter(cronConfig.getWorkDate().get(1))
                || LocalDate.now().isBefore(cronConfig.getWorkDate().get(0))) {
                return;
            }
        }

        // 更新作业流下次计划执行时间
        WorkflowEntity workflow = workflowService.getWorkflow(workflowVersion.getWorkflowId());
        try {
            CronExpression cronExpression = new CronExpression(cronConfig.getCron());
            Date nexDateTime = cronExpression.getNextValidTimeAfter(new Date());
            workflow.setNextDateTime(DateUtil.toLocalDateTime(nexDateTime));
        } catch (ParseException parseException) {
            throw new RuntimeException(parseException);
        }
        workflowRepository.save(workflow);

        // 初始化工作流实例
        WorkflowInstanceEntity workflowInstance =
            WorkflowInstanceEntity.builder().flowId(workflowVersion.getWorkflowId()).status(FlowInstanceStatus.RUNNING)
                .instanceType(InstanceType.AUTO).webConfig(workflowVersion.getWebConfig())
                .versionId(quartzFlowJobRunContext.getFlowVersionId()).nextPlanDateTime(context.getNextFireTime())
                .planStartDateTime(context.getScheduledFireTime()).execStartDateTime(new Date()).build();
        workflowInstance = workflowInstanceRepository.saveAndFlush(workflowInstance);

        // 初始化所有节点的作业实例
        List<String> nodeList = JSON.parseArray(workflowVersion.getNodeList(), String.class);
        List<WorkInstanceEntity> workInstances = new ArrayList<>();
        for (String workId : nodeList) {

            String workVersionId = workRepository.findById(workId).get().getVersionId();

            WorkInstanceEntity metaInstance = WorkInstanceEntity.builder().workId(workId)
                .instanceType(InstanceType.AUTO).quartzHasRun(false).status(InstanceStatus.PENDING)
                .versionId(workVersionId).workflowInstanceId(workflowInstance.getId()).build();

            // 根据作业的调度信息生成计划执行时间和下次执行时间
            VipWorkVersionEntity workVersion = vipWorkVersionRepository.findById(workVersionId).get();
            CronConfig metaCronConfig = JSON.parseObject(workVersion.getCronConfig(), CronConfig.class);
            try {
                CronExpression cronExpression = new CronExpression(metaCronConfig.getCron());
                Date nextValidTimeAfter = cronExpression.getNextValidTimeAfter(new Date());
                metaInstance.setPlanStartDateTime(nextValidTimeAfter);
                metaInstance.setNextPlanDateTime(cronExpression.getNextValidTimeAfter(nextValidTimeAfter));
            } catch (ParseException parseException) {
                throw new RuntimeException(parseException);
            }
            workInstances.add(metaInstance);
        }
        workInstances = workInstanceRepository.saveAllAndFlush(workInstances);

        // 封装工作流的事件体
        WorkflowRunEvent workRunEvent = WorkflowRunEvent.builder().userId(USER_ID.get()).tenantId(TENANT_ID.get())
            .flowInstanceId(workflowInstance.getId())
            .dagStartList(JSON.parseArray(workflowVersion.getDagStartList(), String.class))
            .dagEndList(JSON.parseArray(workflowVersion.getDagEndList(), String.class))
            .nodeList(JSON.parseArray(workflowVersion.getNodeList(), String.class))
            .nodeMapping(
                JSONArray.parseObject(workflowVersion.getNodeMapping(), new TypeReference<List<List<String>>>() {}))
            .build();

        // 每个作业实例都启动一个定时
        for (WorkInstanceEntity workInstance : workInstances) {

            WorkEntity work = workRepository.findById(workInstance.getWorkId()).get();

            // 构建作业的定时上下文
            workRunEvent.setWorkId(workInstance.getWorkId());
            workRunEvent.setVersionId(work.getVersionId());
            workRunEvent.setWorkName(work.getName());
            QuartzWorkJobContext quartzWorkJobRunContext =
                QuartzWorkJobContext.builder().workRunEvent(workRunEvent).build();

            // 封装请求对象
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put("quartzWorkJobContext", JSON.toJSONString(quartzWorkJobRunContext));

            // 获取作业的版本信息
            VipWorkVersionEntity workVersion = vipWorkVersionRepository.findById(work.getVersionId()).get();

            // 提交作业
            JobDetail jobDetail = JobBuilder.newJob(QuartzWorkJob.class).setJobData(jobDataMap).build();
            Trigger trigger = TriggerBuilder.newTrigger()
                .withSchedule(CronScheduleBuilder
                    .cronSchedule(JSON.parseObject(workVersion.getCronConfig(), CronConfig.class).getCron())
                    .withMisfireHandlingInstructionDoNothing())
                .withIdentity(workInstance.getId()).build();

            // 触发作业定时
            try {
                scheduler.scheduleJob(jobDetail, trigger);
                scheduler.start();
            } catch (SchedulerException e) {
                log.error(e.getMessage(), e);
                throw new IsxAppException("发布作业失败");
            }
        }
    }
}
