package com.isxcode.spark.vip.modules.container.service;

import com.isxcode.spark.api.agent.constants.AgentUrl;
import com.isxcode.spark.api.agent.req.StopWorkReq;
import com.isxcode.spark.api.cluster.constants.ClusterNodeStatus;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.backend.api.base.pojos.BaseResponse;
import com.isxcode.spark.backend.api.base.properties.IsxAppProperties;
import com.isxcode.spark.common.utils.http.HttpUtils;
import com.isxcode.spark.modules.cluster.entity.ClusterEntity;
import com.isxcode.spark.modules.cluster.entity.ClusterNodeEntity;
import com.isxcode.spark.modules.cluster.repository.ClusterNodeRepository;
import com.isxcode.spark.modules.cluster.repository.ClusterRepository;
import com.isxcode.spark.modules.container.entity.ContainerEntity;
import com.isxcode.spark.modules.container.repository.ContainerRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class ContainerService {

    private final ContainerRepository containerRepository;

    private final ClusterRepository clusterRepository;

    private final ClusterNodeRepository clusterNodeRepository;

    private final IsxAppProperties isxAppProperties;

    public ContainerEntity getContainer(String containerId) {

        return containerRepository.findById(containerId).orElseThrow(() -> new IsxAppException("容器不存在"));
    }

    public void stopContainer(String clusterId, String appId, Boolean checkCluster) {

        ClusterEntity cluster;
        Optional<ClusterEntity> clusterEntityOptional = clusterRepository.findById(clusterId);
        if (checkCluster) {
            cluster = clusterEntityOptional.orElseThrow(() -> new IsxAppException("集群不存在"));
        } else {
            return;
        }

        // 获取集群节点
        List<ClusterNodeEntity> allEngineNodes =
            clusterNodeRepository.findAllByClusterIdAndStatus(clusterId, ClusterNodeStatus.RUNNING);
        if (allEngineNodes.isEmpty()) {
            throw new IsxAppException("集群不存在可用节点");
        }

        // 节点选择随机数
        ClusterNodeEntity engineNode = allEngineNodes.get(new Random().nextInt(allEngineNodes.size()));

        StopWorkReq stopWorkReq = StopWorkReq.builder().appId(appId).clusterType(cluster.getClusterType())
            .sparkHomePath(engineNode.getSparkHomePath()).agentHomePath(engineNode.getAgentHomePath()).build();
        BaseResponse<?> baseResponse =
            HttpUtils.doPost(genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.STOP_WORK_URL),
                stopWorkReq, BaseResponse.class);

        if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
            throw new IsxAppException(baseResponse.getCode(), baseResponse.getMsg(), baseResponse.getErr());
        }
    }

    public String genHttpUrl(String host, String port, String path) {

        String httpProtocol = isxAppProperties.isUseSsl() ? "https://" : "http://";
        String httpHost = isxAppProperties.isUsePort() ? host + ":" + port : host;

        return httpProtocol + httpHost + path;
    }

}
