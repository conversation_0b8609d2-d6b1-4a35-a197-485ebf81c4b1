package com.isxcode.spark.vip.modules.workflow.instance;

import com.isxcode.spark.api.instance.req.DeleteWorkflowInstanceReq;
import com.isxcode.spark.api.main.constants.ModuleVipCode;
import com.isxcode.spark.common.annotations.successResponse.SuccessResponse;
import com.isxcode.spark.vip.annotation.vip.VipApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "VIP作业流实例模块")
@RequestMapping(ModuleVipCode.VIP_WORKFLOW_INSTANCE)
@RestController
@RequiredArgsConstructor
public class WorkflowInstanceController {

    private final WorkflowInstanceBizService workflowInstanceBizService;

    @VipApi
    @Operation(summary = "删除作业流实例接口")
    @PostMapping("/deleteWorkflowInstance")
    @SuccessResponse("删除成功")
    public void deleteWorkflowInstance(@Valid @RequestBody DeleteWorkflowInstanceReq deleteWorkflowInstanceReq) {

        workflowInstanceBizService.deleteWorkflowInstance(deleteWorkflowInstanceReq);
    }
}
