package com.isxcode.spark.vip.annotation.vip;

import com.alibaba.fastjson2.JSON;
import com.isxcode.spark.api.license.constants.LicenseStatus;
import com.isxcode.spark.api.license.req.LicenseReq;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.backend.api.base.properties.IsxAppProperties;
import com.isxcode.spark.common.utils.path.PathUtils;
import com.isxcode.spark.license.entity.LicenseEntity;
import com.isxcode.spark.license.repository.LicenseRepository;

import java.io.File;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;

import com.isxcode.spark.modules.license.repository.LicenseStore;
import com.isxcode.spark.license.service.LicenseManager;
import global.namespace.fun.io.bios.BIOS;
import global.namespace.truelicense.api.License;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Slf4j
@Component
@RequiredArgsConstructor
public class VipApiAdvice {

    private final LicenseRepository licenseRepository;

    private final LicenseStore licenseStore;

    private final IsxAppProperties isxAppProperties;

    @Pointcut("@annotation(com.isxcode.spark.vip.annotation.vip.VipApi)")
    public void operateVipApi() {}

    @Before(value = "operateVipApi()")
    public void before() {

        // 判断证书是否存在和过期
        if (licenseStore.getLicense() == null) {
            // 在判断数据库中，是否有启动的许可证，有的话重新尝试加载
            Optional<LicenseEntity> licenseEntityOptional = licenseRepository.findByStatus(LicenseStatus.ENABLE);
            License license;
            if (licenseEntityOptional.isPresent()) {
                try {
                    LicenseManager licenseManager = new LicenseManager();
                    licenseManager.install(BIOS.file(Paths
                        .get(PathUtils.parseProjectPath(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()))
                            + File.separator + "license")
                        .resolve(licenseEntityOptional.get().getCode() + ".lic")));
                    licenseManager.verify();
                    license = licenseManager.load();
                } catch (Exception e) {
                    log.debug(e.getMessage(), e);
                    throw new IsxAppException(e.getMessage());
                }
                licenseStore.setLicense(
                    JSON.parseObject(new String(Base64.getDecoder().decode(license.getInfo())), LicenseReq.class));
            } else {
                throw new IsxAppException("500", "请上传许可证");
            }
        } else {
            // 判断是否过期
            if (licenseStore.getLicense().getEndDateTime().isBefore(LocalDateTime.now())
                || licenseStore.getLicense().getStartDateTime().isAfter(LocalDateTime.now())) {
                throw new IsxAppException("500", "许可证已过期");
            }
        }
    }
}
