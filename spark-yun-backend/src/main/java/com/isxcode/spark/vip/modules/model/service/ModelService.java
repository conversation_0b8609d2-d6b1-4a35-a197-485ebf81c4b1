package com.isxcode.spark.vip.modules.model.service;

import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.model.entity.ColumnFormatEntity;
import com.isxcode.spark.modules.model.entity.DataModelColumnEntity;
import com.isxcode.spark.modules.model.entity.DataModelEntity;
import com.isxcode.spark.modules.model.repository.ColumnFormatRepository;
import com.isxcode.spark.modules.model.repository.DataModelColumnRepository;
import com.isxcode.spark.modules.model.repository.DataModelRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ModelService {

    private final ColumnFormatRepository columnFormatRepository;

    private final DataModelColumnRepository dataModelColumnRepository;

    private final DataModelRepository dataModelRepository;

    public ColumnFormatEntity getColumnFormat(String columnFormatId) {

        return columnFormatRepository.findById(columnFormatId).orElseThrow(() -> new IsxAppException("字段标准不存在"));
    }

    public DataModelEntity getDataModel(String dataModelId) {

        return dataModelRepository.findById(dataModelId).orElseThrow(() -> new IsxAppException("数据模型不存在"));
    }

    public DataModelColumnEntity getDataModelColumn(String dataModelColumnId) {

        return dataModelColumnRepository.findById(dataModelColumnId).orElseThrow(() -> new IsxAppException("字段不存在"));
    }

}
