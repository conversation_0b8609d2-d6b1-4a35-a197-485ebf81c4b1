package com.isxcode.spark.vip.modules.container.service;

import com.alibaba.fastjson.JSON;
import com.isxcode.spark.api.agent.constants.AgentUrl;
import com.isxcode.spark.api.agent.req.ContainerCheckReq;
import com.isxcode.spark.api.agent.req.GetWorkStderrLogReq;
import com.isxcode.spark.api.agent.res.ContainerCheckRes;
import com.isxcode.spark.api.agent.res.DeployContainerRes;
import com.isxcode.spark.api.agent.res.GetWorkStderrLogRes;
import com.isxcode.spark.api.cluster.constants.ClusterNodeStatus;
import com.isxcode.spark.api.container.constants.ContainerStatus;
import com.isxcode.spark.api.container.constants.ResourceLevel;
import com.isxcode.spark.api.container.req.*;
import com.isxcode.spark.api.container.res.GetContainerRes;
import com.isxcode.spark.api.container.res.GetContainerRunningLogRes;
import com.isxcode.spark.api.container.res.PageContainerRes;
import com.isxcode.spark.api.work.constants.WorkLog;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.backend.api.base.pojos.BaseResponse;
import com.isxcode.spark.backend.api.base.properties.IsxAppProperties;
import com.isxcode.spark.common.utils.http.HttpUrlUtils;
import com.isxcode.spark.common.utils.http.HttpUtils;
import com.isxcode.spark.modules.cluster.entity.ClusterEntity;
import com.isxcode.spark.modules.cluster.entity.ClusterNodeEntity;
import com.isxcode.spark.modules.cluster.repository.ClusterNodeRepository;
import com.isxcode.spark.modules.cluster.service.ClusterService;
import com.isxcode.spark.modules.container.entity.ContainerEntity;
import com.isxcode.spark.modules.datasource.service.DatasourceService;
import com.isxcode.spark.modules.work.service.WorkConfigService;
import com.isxcode.spark.modules.container.mapper.ContainerMapper;
import com.isxcode.spark.modules.container.repository.ContainerRepository;
import com.isxcode.spark.vip.modules.container.run.ContainerRun;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class ContainerBizService {

    private final ContainerRepository containerRepository;

    private final ContainerMapper containerMapper;

    private final ContainerService containerService;

    private final ContainerRun containerRun;

    private final ClusterNodeRepository clusterNodeRepository;

    private final ClusterService clusterService;

    private final IsxAppProperties isxAppProperties;

    private final DatasourceService datasourceService;

    private final WorkConfigService workConfigService;

    private final HttpUrlUtils httpUrlUtils;

    public void addContainer(AddContainerReq addContainerReq) {

        ContainerEntity container = containerMapper.addContainerToContainerEntity(addContainerReq);

        if (!ResourceLevel.CUSTOM.equals(addContainerReq.getResourceLevel())) {
            container.setSparkConfig(
                JSON.toJSONString(workConfigService.initSparkConfig(addContainerReq.getResourceLevel())));
        }
        container.setStatus(ContainerStatus.NEW);
        containerRepository.save(container);
    }

    public void updateContainer(UpdateContainerReq updateContainerReq) {

        ContainerEntity container = containerService.getContainer(updateContainerReq.getId());

        container = containerMapper.updateContainerToContainerEntity(updateContainerReq, container);

        if (!ResourceLevel.CUSTOM.equals(updateContainerReq.getResourceLevel())) {
            container.setSparkConfig(
                JSON.toJSONString(workConfigService.initSparkConfig(updateContainerReq.getResourceLevel())));
        } else {
            container.setSparkConfig(updateContainerReq.getSparkConfig());
        }

        containerRepository.save(container);
    }

    public Page<PageContainerRes> pageContainer(PageContainerReq pageContainerReq) {

        Page<ContainerEntity> entityPage = containerRepository.pageContainer(pageContainerReq.getSearchKeyWord(),
            PageRequest.of(pageContainerReq.getPage(), pageContainerReq.getPageSize()));

        Page<PageContainerRes> result = entityPage.map(containerMapper::containerEntityToPageContainerRes);
        result.getContent().forEach(e -> {
            e.setClusterName(clusterService.getClusterName(e.getClusterId()));
            e.setDatasourceName(datasourceService.getDatasourceName(e.getDatasourceId()));
        });
        return result;
    }

    public void deleteContainer(DeleteContainerReq deleteContainerReq) {

        ContainerEntity container = containerService.getContainer(deleteContainerReq.getId());

        containerService.stopContainer(container.getClusterId(), container.getApplicationId(), false);

        containerRepository.delete(container);
    }

    public void startContainer(StartContainerReq startContainerReq) {

        ContainerEntity container = containerService.getContainer(startContainerReq.getId());

        // 启动中的,不可以二次启动
        if (ContainerStatus.DEPLOYING.equals(container.getStatus())) {
            throw new IsxAppException("正在启动,请停止重试");
        }

        // 启动中的,不可以二次启动
        if (ContainerStatus.RUNNING.equals(container.getStatus())) {
            throw new IsxAppException("运行中,请停止后重试");
        }

        container.setStatus(ContainerStatus.DEPLOYING);
        containerRepository.save(container);

        // 异步发布容器
        CompletableFuture.supplyAsync(() -> {
            try {
                DeployContainerRes deployContainerRes = containerRun.deployContainer(container);
                deployContainerRes.setStatus(ContainerStatus.RUNNING);
                return deployContainerRes;
            } catch (IsxAppException ex) {
                return DeployContainerRes.builder().status(ContainerStatus.FAIL).errLog(ex.getMsg()).build();
            }
        }).whenComplete((result, throwable) -> {
            // 持久化到数据库
            ContainerEntity containerNew = containerService.getContainer(container.getId());
            if (result.getErrLog() != null) {
                containerNew.setSubmitLog(containerNew.getSubmitLog() + result.getErrLog());
            }
            containerNew.setApplicationId(result.getAppId());
            containerNew.setStatus(result.getStatus());
            containerNew.setPort(result.getPort());
            containerRepository.save(containerNew);
        });

    }

    public void checkContainer(CheckContainerReq checkContainerReq) {

        // 获取容器
        ContainerEntity container = containerService.getContainer(checkContainerReq.getId());

        if (ContainerStatus.DEPLOYING.equals(container.getStatus())) {
            throw new IsxAppException("启动中,请稍后重试");
        }

        // 获取集群节点
        List<ClusterNodeEntity> allEngineNodes =
            clusterNodeRepository.findAllByClusterIdAndStatus(container.getClusterId(), ClusterNodeStatus.RUNNING);
        if (allEngineNodes.isEmpty()) {
            throw new IsxAppException("集群不存在可用节点");
        }

        // 节点选择随机数
        ClusterNodeEntity engineNode = allEngineNodes.get(new Random().nextInt(allEngineNodes.size()));

        // 再次调用容器的check接口，确认容器是否成功启动
        ContainerCheckReq containerCheckReq =
            ContainerCheckReq.builder().port(String.valueOf(container.getPort())).build();
        BaseResponse<?> baseResponse;
        try {
            baseResponse = HttpUtils.doPost(
                genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.CONTAINER_CHECK_URL),
                containerCheckReq, BaseResponse.class);
        } catch (HttpServerErrorException e) {
            throw new IsxAppException("检查集群状态:" + e.getMessage());
        }

        if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
            container.setStatus(ContainerStatus.STOP);
        }

        ContainerCheckRes containerCheckRes =
            JSON.parseObject(JSON.toJSONString(baseResponse), ContainerCheckRes.class);
        if ("200".equals(containerCheckRes.getCode())) {
            container.setStatus(ContainerStatus.RUNNING);
        } else {
            container.setStatus(ContainerStatus.STOP);
        }
    }

    public String genHttpUrl(String host, String port, String path) {

        String httpProtocol = isxAppProperties.isUseSsl() ? "https://" : "http://";
        String httpHost = isxAppProperties.isUsePort() ? host + ":" + port : host;

        return httpProtocol + httpHost + path;
    }

    public void stopContainer(StopContainerReq stopContainerReq) {

        // 获取容器
        ContainerEntity container = containerService.getContainer(stopContainerReq.getId());

        if (ContainerStatus.STOP.equals(container.getStatus())) {
            throw new IsxAppException("已停止");
        }

        if (Strings.isEmpty(container.getApplicationId())) {
            throw new IsxAppException("项目未启动成功");
        }

        // 关闭容器
        containerService.stopContainer(container.getClusterId(), container.getApplicationId(), true);

        // 修改状态
        container.setStatus(ContainerStatus.STOP);
        container.setSubmitLog(container.getSubmitLog() + LocalDateTime.now() + WorkLog.ERROR_INFO + "手动中止成功");
        containerRepository.save(container);
    }

    public GetContainerRes getContainer(GetContainerReq getContainerReq) {

        ContainerEntity container = containerService.getContainer(getContainerReq.getId());

        return containerMapper.containerEntityToGetContainerRes(container);
    }

    public GetContainerRunningLogRes getContainerRunningLog(GetContainerRunningLogReq getContainerRunningLogReq) {

        // 获取容器信息
        ContainerEntity container = containerService.getContainer(getContainerRunningLogReq.getId());

        // 获取容器集群
        ClusterEntity cluster = clusterService.getCluster(container.getClusterId());

        // 随机集群节点
        List<ClusterNodeEntity> allEngineNodes =
            clusterNodeRepository.findAllByClusterIdAndStatus(cluster.getId(), ClusterNodeStatus.RUNNING);
        if (allEngineNodes.isEmpty()) {
            throw new IsxAppException("当前集群不可用");
        }
        ClusterNodeEntity engineNode = allEngineNodes.get(new Random().nextInt(allEngineNodes.size()));

        // 封装请求体
        GetWorkStderrLogReq getWorkStderrLogReq = GetWorkStderrLogReq.builder().appId(container.getApplicationId())
            .clusterType(cluster.getClusterType()).sparkHomePath(engineNode.getSparkHomePath()).build();

        // 获取运行日志
        BaseResponse<?> baseResponse = HttpUtils.doPost(
            httpUrlUtils.genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.GET_WORK_STDERR_LOG_URL),
            getWorkStderrLogReq, BaseResponse.class);
        if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
            throw new IsxAppException("获取日志异常 : " + baseResponse.getMsg());
        }
        GetWorkStderrLogRes yagGetLogRes =
            JSON.parseObject(JSON.toJSONString(baseResponse.getData()), GetWorkStderrLogRes.class);

        // 保存日志
        container.setRunningLog(yagGetLogRes.getLog());
        containerRepository.save(container);

        // 返回日志
        return GetContainerRunningLogRes.builder().id(container.getId()).runningLog(container.getRunningLog()).build();
    }
}
