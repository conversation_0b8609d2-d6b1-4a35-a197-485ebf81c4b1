package com.isxcode.spark.vip.modules.work.service;

import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.workflow.entity.WorkflowVersionEntity;
import com.isxcode.spark.modules.workflow.repository.WorkflowVersionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class VipWorkService {

    private final WorkflowVersionRepository workflowVersionRepository;

    public WorkflowVersionEntity getWorkflowVersion(String workflowVersionId) {

        return workflowVersionRepository.findById(workflowVersionId).orElseThrow(() -> new IsxAppException("作业流版本异常"));
    }
}
