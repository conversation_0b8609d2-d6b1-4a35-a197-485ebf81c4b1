package com.isxcode.spark.vip.modules.work.quartz;

import static com.isxcode.spark.common.config.CommonConfig.TENANT_ID;
import static com.isxcode.spark.common.config.CommonConfig.USER_ID;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.isxcode.spark.api.alarm.constants.AlarmEventType;
import com.isxcode.spark.api.instance.constants.FlowInstanceStatus;
import com.isxcode.spark.api.instance.constants.InstanceStatus;
import com.isxcode.spark.api.instance.constants.InstanceType;
import com.isxcode.spark.api.work.dto.CronConfig;
import com.isxcode.spark.modules.alarm.service.AlarmService;
import com.isxcode.spark.modules.work.entity.WorkEntity;
import com.isxcode.spark.modules.work.entity.WorkInstanceEntity;
import com.isxcode.spark.modules.work.repository.WorkInstanceRepository;
import com.isxcode.spark.modules.work.repository.WorkRepository;
import com.isxcode.spark.modules.workflow.entity.WorkflowEntity;
import com.isxcode.spark.modules.workflow.entity.WorkflowInstanceEntity;
import com.isxcode.spark.modules.workflow.repository.WorkflowInstanceRepository;
import com.isxcode.spark.modules.workflow.repository.WorkflowRepository;
import com.isxcode.spark.modules.workflow.run.WorkflowRunEvent;
import com.isxcode.spark.modules.workflow.entity.WorkflowVersionEntity;
import com.isxcode.spark.modules.workflow.repository.WorkflowVersionRepository;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.isxcode.spark.modules.workflow.service.WorkflowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class QuartzAllFlowJob implements Job {

    private final WorkflowVersionRepository workflowVersionRepository;

    private final WorkflowInstanceRepository workflowInstanceRepository;

    private final WorkInstanceRepository workInstanceRepository;

    private final WorkRepository workRepository;

    private final ApplicationEventPublisher eventPublisher;

    private final WorkflowRepository workflowRepository;

    private final WorkflowService workflowService;

    private final AlarmService alarmService;

    @Override
    public void execute(JobExecutionContext context) {

        // 从context中获取作业流的运行参数
        QuartzFlowJobContext quartzFlowJobRunContext =
            JSON.parseObject(String.valueOf(context.getJobDetail().getJobDataMap().get("quartzFlowJobRunContext")),
                QuartzFlowJobContext.class);

        // 初始化异步线程中的上下文
        USER_ID.set(quartzFlowJobRunContext.getUserId());
        TENANT_ID.set(quartzFlowJobRunContext.getTenantId());

        // 获取工作流的版本信息
        WorkflowVersionEntity workflowVersion =
            workflowVersionRepository.findById(quartzFlowJobRunContext.getFlowVersionId()).get();

        // 更新一下次执行时间
        WorkflowEntity workflow = workflowService.getWorkflow(workflowVersion.getWorkflowId());
        CronConfig cronConfig = JSON.parseObject(workflowVersion.getCronConfig(), CronConfig.class);

        // 生效时间限制
        if (cronConfig.getWorkDate() != null && cronConfig.getWorkDate().size() == 2) {
            if (LocalDate.now().isAfter(cronConfig.getWorkDate().get(1))
                || LocalDate.now().isBefore(cronConfig.getWorkDate().get(0))) {
                return;
            }
        }

        try {
            CronExpression cronExpression = new CronExpression(cronConfig.getCron());
            Date nexDateTime = cronExpression.getNextValidTimeAfter(new Date());
            workflow.setNextDateTime(DateUtil.toLocalDateTime(nexDateTime));
        } catch (ParseException parseException) {
            throw new RuntimeException(parseException);
        }
        workflowRepository.save(workflow);

        // 初始化工作流实例
        WorkflowInstanceEntity workflowInstance =
            WorkflowInstanceEntity.builder().flowId(workflowVersion.getWorkflowId()).status(FlowInstanceStatus.RUNNING)
                .instanceType(InstanceType.AUTO).versionId(quartzFlowJobRunContext.getFlowVersionId())
                .webConfig(workflowVersion.getWebConfig()).planStartDateTime(context.getScheduledFireTime())
                .nextPlanDateTime(context.getNextFireTime()).execStartDateTime(new Date()).build();
        workflowInstance = workflowInstanceRepository.saveAndFlush(workflowInstance);

        // 初始化作业实例
        List<String> nodeList = com.alibaba.fastjson.JSON.parseArray(workflowVersion.getNodeList(), String.class);
        List<WorkInstanceEntity> workInstances = new ArrayList<>();
        List<WorkEntity> allWorks = workRepository.findAllById(nodeList);
        for (WorkEntity metaWork : allWorks) {
            WorkInstanceEntity metaInstance = WorkInstanceEntity.builder().workId(metaWork.getId())
                .versionId(metaWork.getVersionId()).instanceType(InstanceType.AUTO).status(InstanceStatus.PENDING)
                .nextPlanDateTime(context.getNextFireTime()).planStartDateTime(context.getScheduledFireTime())
                .quartzHasRun(true).workflowInstanceId(workflowInstance.getId()).build();
            workInstances.add(metaInstance);
        }

        workInstanceRepository.saveAllAndFlush(workInstances);

        // 直接执行一次作业流的点击
        List<String> startNodes = com.alibaba.fastjson.JSON.parseArray(workflowVersion.getDagStartList(), String.class);
        List<String> endNodes = com.alibaba.fastjson.JSON.parseArray(workflowVersion.getDagEndList(), String.class);
        List<List<String>> nodeMapping = com.alibaba.fastjson.JSON.parseObject(workflowVersion.getNodeMapping(),
            new TypeReference<List<List<String>>>() {});

        // 开始执行，告警机制
        if (InstanceType.AUTO.equals(workflowInstance.getInstanceType())) {
            alarmService.sendWorkflowMessage(workflowInstance, AlarmEventType.START_RUN);
        }

        // 封装event推送时间，开始执行任务
        // 异步触发工作流
        List<WorkEntity> startNodeWorks = workRepository.findAllByWorkIds(startNodes);
        for (WorkEntity work : startNodeWorks) {
            WorkflowRunEvent metaEvent = WorkflowRunEvent.builder().workId(work.getId()).workName(work.getName())
                .dagEndList(endNodes).dagStartList(startNodes).flowInstanceId(workflowInstance.getId())
                .versionId(work.getVersionId()).nodeMapping(nodeMapping).nodeList(nodeList).tenantId(TENANT_ID.get())
                .userId(USER_ID.get()).build();
            eventPublisher.publishEvent(metaEvent);
        }
    }
}
