package com.isxcode.spark.vip.modules.layer.service;

import com.isxcode.spark.api.layer.req.*;
import com.isxcode.spark.api.layer.res.GetParentParentLayerRes;
import com.isxcode.spark.api.layer.res.LayerPageRes;
import com.isxcode.spark.api.layer.res.RecursiveLayerRes;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.layer.entity.LayerEntity;
import com.isxcode.spark.modules.layer.mapper.LayerMapper;
import com.isxcode.spark.modules.layer.repository.LayerRepository;
import com.isxcode.spark.modules.model.repository.DataModelRepository;
import com.isxcode.spark.modules.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class LayerBizService {

    private final LayerRepository layerRepository;

    private final LayerService layerService;

    private final LayerMapper layerMapper;

    private final UserService userService;

    private final DataModelRepository dataModelRepository;

    public void addLayer(AddLayerReq addLayerReq) {

        // 判断名称重复
        if (layerRepository.existsByNameAndParentLayerId(addLayerReq.getName(), addLayerReq.getParentLayerId())) {
            throw new IsxAppException("数据分层名称重复");
        }

        // 映射字段
        LayerEntity layer = layerMapper.addLayerReqToLayerEntity(addLayerReq);

        // 判断是否有父级
        if (!Strings.isEmpty(addLayerReq.getParentLayerId())) {

            // 判断父级是否存在
            LayerEntity parentLayer = layerService.getLayer(addLayerReq.getParentLayerId());

            // 配置父级链
            layer.setParentIdList(parentLayer.getParentIdList() == null ? parentLayer.getId()
                : parentLayer.getParentIdList() + "." + parentLayer.getId());
            layer.setParentNameList(parentLayer.getParentNameList() == null ? parentLayer.getName()
                : parentLayer.getParentNameList() + "." + parentLayer.getName());
        } else {
            layer.setParentLayerId(null);
        }

        // 持久化数据
        layerRepository.save(layer);
    }

    public void updateLayer(UpdateLayerReq updateLayerReq) {

        // 判断数据分层是否存在
        LayerEntity layer = layerService.getLayer(updateLayerReq.getId());

        // 判断名称重复
        layerRepository.findByNameAndParentLayerId(updateLayerReq.getName(), layer.getParentLayerId())
            .ifPresent(layerEntity -> {
                if (!layerEntity.getId().equals(updateLayerReq.getId())) {
                    throw new IsxAppException("数据分层名称重复");
                }
            });

        // 修复子级名称，查询所有包含
        if (!updateLayerReq.getName().equals(layer.getName())) {

            String oldParentNameList =
                layer.getParentNameList() == null ? layer.getName() : layer.getParentNameList() + "." + layer.getName();
            String newParentNameList = layer.getParentNameList() == null ? updateLayerReq.getName()
                : layer.getParentNameList() + "." + updateLayerReq.getName();

            // 查询所有相关的数据分层
            List<LayerEntity> parentNameListLike = layerRepository.findAllByParentIdListLike(
                (layer.getParentIdList() == null ? layer.getId() : layer.getParentIdList() + "." + layer.getId())
                    + "%");
            parentNameListLike.forEach(layerEntity -> {
                layerEntity
                    .setParentNameList(layerEntity.getParentNameList().replace(oldParentNameList, newParentNameList));
                layerRepository.save(layerEntity);
            });
        }

        // 持久化数据
        layer.setName(updateLayerReq.getName());
        layer.setTableRule(updateLayerReq.getTableRule());
        layer.setRemark(updateLayerReq.getRemark());
        layerRepository.save(layer);
    }

    public Page<LayerPageRes> pageLayer(PageLayerReq pageLayerReq) {

        if (pageLayerReq.getSearchKeyWord() == null) {
            pageLayerReq.setSearchKeyWord("");
        }

        if (Strings.isEmpty(pageLayerReq.getParentLayerId())) {
            pageLayerReq.setParentLayerId(null);
        }

        Page<LayerEntity> layerPage = layerRepository.pageLayer(pageLayerReq.getSearchKeyWord(),
            pageLayerReq.getParentLayerId(), PageRequest.of(pageLayerReq.getPage(), pageLayerReq.getPageSize()));

        Page<LayerPageRes> map = layerPage.map(layerMapper::layerEntityToLayerPageRes);

        map.getContent().forEach(layerPageRes -> {
            layerPageRes.setCreateUsername(userService.getUserName(layerPageRes.getCreateBy()));
        });

        return map;
    }

    public void deleteLayer(DeleteLayerReq deleteLayerReq) {

        // 判断数据分层是否存在
        LayerEntity layer = layerService.getLayer(deleteLayerReq.getId());

        // 分层下有模型无法删除
        if (dataModelRepository.existsByLayerId(layer.getId())) {
            throw new IsxAppException("分层下有模型无法删除");
        }

        // 判断子级目录是否有模型
        List<LayerEntity> allLayerList = layerRepository.findAllByParentIdListLike(
            (layer.getParentIdList() == null ? layer.getId() : layer.getParentIdList() + "." + layer.getId()) + "%");
        List<String> layerIdList = allLayerList.stream().map(LayerEntity::getId).collect(Collectors.toList());
        if (dataModelRepository.existsByLayerIdIn(layerIdList)) {
            throw new IsxAppException("分层下有模型无法删除");
        }

        // 删除子级数据
        layerRepository.deleteAll(allLayerList);

        // 删除自己
        layerRepository.delete(layer);
    }

    public Page<LayerPageRes> searchLayer(SearchLayerReq searchLayerReq) {

        if (searchLayerReq.getSearchKeyWord() == null) {
            searchLayerReq.setSearchKeyWord("");
        }

        Page<LayerEntity> layerPage = layerRepository.searchAll(searchLayerReq.getSearchKeyWord(),
            PageRequest.of(searchLayerReq.getPage(), searchLayerReq.getPageSize()));

        Page<LayerPageRes> map = layerPage.map(layerMapper::layerEntityToLayerPageRes);
        map.getContent().forEach(layerPageRes -> {
            layerPageRes.setCreateUsername(userService.getUserName(layerPageRes.getCreateBy()));
            layerPageRes.setFullPathName(layerPageRes.getParentNameList() == null ? layerPageRes.getName()
                : layerPageRes.getParentNameList() + "." + layerPageRes.getName());
        });

        return map;
    }

    public GetParentParentLayerRes getParentParentLayer(GetParentParentLayerReq getParentParentLayerReq) {

        // 初始化返回值
        GetParentParentLayerRes getParentParentLayerRes = new GetParentParentLayerRes();

        // 获取当前分层
        LayerEntity layer = layerService.getLayer(getParentParentLayerReq.getId());
        getParentParentLayerRes.setLayerId(layer.getId());

        // 获取父父级
        if (Strings.isEmpty(layer.getParentLayerId())) {
            getParentParentLayerRes.setParentLayerId(null);
            getParentParentLayerRes.setParentParentLayerId(null);
        } else {
            LayerEntity parentLayer = layerService.getLayer(layer.getParentLayerId());
            getParentParentLayerRes.setParentLayerId(parentLayer.getId());
            getParentParentLayerRes.setParentParentLayerId(parentLayer.getParentLayerId());
        }

        return getParentParentLayerRes;
    }

    public RecursiveLayerRes recursiveLayer(RecursiveLayerReq recursiveLayerReq) {

        // 判断当前分层是否存在
        LayerEntity layer = layerService.getLayer(recursiveLayerReq.getId());

        // 查询和当前分层相关的数据
        List<LayerEntity> allLayer = layerRepository.findAllByParentIdListLike("%" + layer.getId() + "%");

        // 封装map
        Map<String, List<LayerEntity>> parentLayerMap =
            allLayer.stream().collect(Collectors.groupingBy(LayerEntity::getParentLayerId));
        RecursiveLayerRes recursiveLayerRes = layerMapper.layerEntityToRecursiveLayerRes(layer);

        // 递归查询
        getRecursiveLayer(recursiveLayerRes, parentLayerMap);
        return recursiveLayerRes;
    }

    public void getRecursiveLayer(RecursiveLayerRes recursiveLayerRes, Map<String, List<LayerEntity>> parentLayerMap) {

        List<LayerEntity> childrenLayerList = parentLayerMap.get(recursiveLayerRes.getId());
        if (childrenLayerList == null || childrenLayerList.isEmpty()) {
            recursiveLayerRes.setChildren(new ArrayList<>());
            return;
        }
        List<RecursiveLayerRes> layerRes = layerMapper.layerEntityListToRecursiveLayerResList(childrenLayerList);
        recursiveLayerRes.setChildren(layerRes);
        layerRes.forEach(layer -> getRecursiveLayer(layer, parentLayerMap));
    }
}
