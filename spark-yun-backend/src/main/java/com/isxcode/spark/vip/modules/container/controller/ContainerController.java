package com.isxcode.spark.vip.modules.container.controller;

import com.isxcode.spark.api.container.req.*;
import com.isxcode.spark.api.container.res.GetContainerRes;
import com.isxcode.spark.api.container.res.GetContainerRunningLogRes;
import com.isxcode.spark.api.container.res.PageContainerRes;
import com.isxcode.spark.api.main.constants.ModuleVipCode;
import com.isxcode.spark.api.user.constants.RoleType;
import com.isxcode.spark.common.annotations.successResponse.SuccessResponse;
import com.isxcode.spark.vip.annotation.vip.VipApi;
import com.isxcode.spark.vip.modules.container.service.ContainerBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "spark容器模块")
@RequestMapping(ModuleVipCode.VIP_CONTAINER)
@RestController
@RequiredArgsConstructor
public class ContainerController {

    private final ContainerBizService containerBizService;

    @VipApi
    @Operation(summary = "创建容器接口")
    @PostMapping("/addContainer")
    @SuccessResponse("创建成功")
    public void addContainer(@Valid @RequestBody AddContainerReq addContainerReq) {

        containerBizService.addContainer(addContainerReq);
    }

    @VipApi
    @Operation(summary = "更新容器接口")
    @PostMapping("/updateContainer")
    @SuccessResponse("更新成功")
    public void updateContainer(@Valid @RequestBody UpdateContainerReq updateContainerReq) {

        containerBizService.updateContainer(updateContainerReq);
    }

    @VipApi
    @Operation(summary = "分页查询容器接口")
    @PostMapping("/pageContainer")
    @SuccessResponse("查询成功")
    public Page<PageContainerRes> pageContainer(@Valid @RequestBody PageContainerReq pageContainerReq) {

        return containerBizService.pageContainer(pageContainerReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_ADMIN})
    @Operation(summary = "删除容器接口")
    @PostMapping("/deleteContainer")
    @SuccessResponse("删除成功")
    public void deleteContainer(@Valid @RequestBody DeleteContainerReq deleteContainerReq) {

        containerBizService.deleteContainer(deleteContainerReq);
    }

    @VipApi
    @Operation(summary = "启动容器接口")
    @PostMapping("/startContainer")
    @SuccessResponse("启动中")
    public void startContainer(@Valid @RequestBody StartContainerReq startContainerReq) {

        containerBizService.startContainer(startContainerReq);
    }

    @VipApi
    @Operation(summary = "检测容器状态接口")
    @PostMapping("/checkContainer")
    @SuccessResponse("检测完成")
    public void checkContainer(@Valid @RequestBody CheckContainerReq checkContainerReq) {

        containerBizService.checkContainer(checkContainerReq);
    }

    @VipApi
    @Operation(summary = "停止容器状态接口")
    @PostMapping("/stopContainer")
    @SuccessResponse("停止成功")
    public void stopContainer(@Valid @RequestBody StopContainerReq stopContainerReq) {

        containerBizService.stopContainer(stopContainerReq);
    }

    @VipApi
    @Operation(summary = "获取单个容器信息接口")
    @PostMapping("/getContainer")
    @SuccessResponse("获取成功")
    public GetContainerRes getContainer(@Valid @RequestBody GetContainerReq getContainerReq) {

        return containerBizService.getContainer(getContainerReq);
    }

    @VipApi
    @Operation(summary = "获取容器运行日志接口")
    @PostMapping("/getContainerRunningLog")
    @SuccessResponse("获取成功")
    public GetContainerRunningLogRes getContainerRunningLog(
        @Valid @RequestBody GetContainerRunningLogReq getContainerRunningLogReq) {

        return containerBizService.getContainerRunningLog(getContainerRunningLogReq);
    }

}
