package com.isxcode.spark.vip.modules.work.instance;

import com.isxcode.spark.api.instance.constants.InstanceStatus;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.work.entity.VipWorkVersionEntity;
import com.isxcode.spark.modules.work.entity.WorkInstanceEntity;
import com.isxcode.spark.modules.work.repository.VipWorkVersionRepository;
import com.isxcode.spark.modules.work.repository.WorkInstanceRepository;
import com.isxcode.spark.modules.work.run.WorkExecutor;
import com.isxcode.spark.modules.work.run.WorkExecutorFactory;
import com.isxcode.spark.modules.work.run.WorkRunContext;
import com.isxcode.spark.vip.modules.work.service.VipWorkBizService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Optional;

import static com.isxcode.spark.modules.workflow.run.WorkflowUtils.genWorkRunContext;


@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class WorkInstanceBizService {

    private final WorkInstanceRepository workInstanceRepository;

    private final WorkInstanceMapper workInstanceMapper;

    private final VipWorkVersionRepository vipWorkVersionRepository;

    private final WorkExecutorFactory workExecutorFactory;

    private final VipWorkBizService vipWorkBizService;

    public void restartInstance(String instanceId) {

        WorkInstanceEntity workInstance =
            workInstanceRepository.findById(instanceId).orElseThrow(() -> new IsxAppException("实例不存在"));

        // 获取版本信息
        if (workInstance.getVersionId() == null) {
            throw new IsxAppException("只有发布调度的作业，才支持重跑");
        }

        Optional<VipWorkVersionEntity> workVersionEntityOptional =
            vipWorkVersionRepository.findById(workInstance.getVersionId());
        if (!workVersionEntityOptional.isPresent()) {
            throw new IsxAppException("版本未生成");
        }

        // 将版本装成调度传参数对象
        WorkRunContext workRunContext = genWorkRunContext(workVersionEntityOptional.get());

        // 重新初始化例状态
        workInstance.setStatus(InstanceStatus.RUNNING);
        workInstance.setExecEndDateTime(null);
        workInstance.setResultData("");
        workInstance.setYarnLog("");
        workInstance.setSubmitLog("");
        workInstanceRepository.saveAndFlush(workInstance);

        // 异步执行作业
        workRunContext.setInstanceId(workInstance.getId());
        WorkExecutor workExecutor = workExecutorFactory.create(workRunContext.getWorkType());
        workExecutor.asyncExecute(workRunContext);
    }

    public void stopInstance(String instanceId) {

        // // 判断实例是手动还是自动
        // WorkInstanceEntity workInstanceEntity =
        // workInstanceRepository.findById(instanceId).get();
        //
        // WorkEntity workEntity =
        // workRepository.findById(workInstanceEntity.getWorkId()).get();
        //
        // if (InstanceStatus.SUCCESS.equals(workInstanceEntity.getStatus())) {
        // throw new IsxAppException("已经成功，无法中止");
        // }
        //
        // if (InstanceStatus.ABORT.equals(workInstanceEntity.getStatus())) {
        // throw new IsxAppException("已中止");
        // }
        //
        // if (!WorkType.QUERY_SPARK_SQL.equals(workEntity.getWorkType())) {
        // throw new IsxAppException("只有sparkSql作业支持中止");
        // }
        //
        // if (Strings.isEmpty(workInstanceEntity.getSparkStarRes())) {
        // throw new IsxAppException("作业未提交到yarn，请稍后再试");
        // }
        //
        // String clusterId;
        // if (InstanceType.MANUAL.equals(workInstanceEntity.getInstanceType())) {
        // // 从作业配置中获取clusterId
        // WorkConfigEntity workConfigEntity =
        // workConfigRepository.findById(workEntity.getConfigId()).get();
        // clusterId = workConfigEntity.getClusterConfig();
        // } else {
        // // 从版本中获取clusterId
        // Optional<VipWorkVersionEntity> vipWorkVersionEntityOptional =
        // vipWorkVersionRepository
        // .findById(workInstanceEntity.getVersionId());
        // if (!vipWorkVersionEntityOptional.isPresent()) {
        // throw new IsxAppException("请稍后再试");
        // }
        // clusterId = vipWorkVersionEntityOptional.get().getClusterId();
        // }
        //
        // Optional<ClusterEntity> clusterEntityOptional =
        // clusterRepository.findById(clusterId);
        //
        // List<ClusterNodeEntity> allEngineNodes =
        // clusterNodeRepository.findAllByClusterIdAndStatus(clusterId,
        // ClusterNodeStatus.RUNNING);
        // if (allEngineNodes.isEmpty()) {
        // throw new WorkRunException(LocalDateTime.now() + WorkLog.ERROR_INFO + "申请资源失败
        // : 集群不存在可用节点，请切换一个集群 \n");
        // }
        //
        // // 节点选择随机数
        // ClusterNodeEntity engineNode = allEngineNodes.get(new
        // Random().nextInt(allEngineNodes.size()));
        //
        // // 解析实例的状态信息
        // RunWorkRes wokRunWorkRes =
        // JSON.parseObject(workInstanceEntity.getSparkStarRes(), RunWorkRes.class);
        //
        // Map<String, String> paramsMap = new HashMap<>();
        // paramsMap.put("appId", wokRunWorkRes.getAppId());
        // paramsMap.put("agentType", clusterEntityOptional.get().getClusterType());
        // BaseResponse<?> baseResponse = HttpUtils.doGet(
        // httpUrlUtils.genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(),
        // "/yag/stopJob"), paramsMap,
        // null, BaseResponse.class);
        //
        // if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
        // throw new IsxAppException(baseResponse.getCode(), baseResponse.getMsg(),
        // baseResponse.getErr());
        // }
        //
        // // 修改实例状态
        // workInstanceEntity.setStatus(InstanceStatus.ABORT);
        // String submitLog = workInstanceEntity.getSubmitLog() + LocalDateTime.now() +
        // WorkLog.SUCCESS_INFO + "已中止 \n";
        // workInstanceEntity.setSubmitLog(submitLog);
        // workInstanceEntity.setExecEndDateTime(new Date());
        // workInstanceRepository.saveAndFlush(workInstanceEntity);
    }

    public void deleteInstance(String instanceId) {

        workInstanceRepository.deleteById(instanceId);
    }
}
