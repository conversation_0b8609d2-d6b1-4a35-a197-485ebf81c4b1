package com.isxcode.spark.vip.modules.work.instance;

import com.isxcode.spark.api.main.constants.ModuleVipCode;
import com.isxcode.spark.api.user.constants.RoleType;
import com.isxcode.spark.backend.api.base.constants.SecurityConstants;
import com.isxcode.spark.common.annotations.successResponse.SuccessResponse;
import com.isxcode.spark.vip.annotation.vip.VipApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "VIP作业实例模块")
@RequestMapping(ModuleVipCode.VIP_WORK_INSTANCE)
@RestController
@RequiredArgsConstructor
public class WorkInstanceController {

    private final WorkInstanceBizService workInstanceBizService;

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "重跑作业实例接口")
    @GetMapping("/restartInstance")
    @SuccessResponse("重跑成功")
    @Parameter(name = SecurityConstants.HEADER_TENANT_ID, description = "租户id", required = true,
        in = ParameterIn.HEADER, schema = @Schema(type = "string"))
    public void restartInstance(@Schema(description = "实例唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc")
    @RequestParam String instanceId) {

        workInstanceBizService.restartInstance(instanceId);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "中止作业实例接口")
    @GetMapping("/stopInstance")
    @SuccessResponse("中止成功")
    @Parameter(name = SecurityConstants.HEADER_TENANT_ID, description = "租户id", required = true,
        in = ParameterIn.HEADER, schema = @Schema(type = "string"))
    public void stopInstance(@Schema(description = "实例唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc")
    @RequestParam String instanceId) {

        workInstanceBizService.stopInstance(instanceId);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "删除作业实例接口")
    @GetMapping("/deleteInstance")
    @SuccessResponse("删除成功")
    @Parameter(name = SecurityConstants.HEADER_TENANT_ID, description = "租户id", required = true,
        in = ParameterIn.HEADER, schema = @Schema(type = "string"))
    public void deleteInstance(@Schema(description = "实例唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc")
    @RequestParam String instanceId) {

        workInstanceBizService.deleteInstance(instanceId);
    }
}
