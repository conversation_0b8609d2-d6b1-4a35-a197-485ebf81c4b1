package com.isxcode.spark.vip.modules.workflow.version;

import com.isxcode.spark.modules.workflow.entity.WorkflowConfigEntity;
import com.isxcode.spark.modules.workflow.entity.WorkflowEntity;
import com.isxcode.spark.modules.workflow.entity.WorkflowVersionEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface WorkflowVersionMapper {

    @Mapping(target = "workVersionMap", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "createDateTime", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "lastModifiedDateTime", ignore = true)
    @Mapping(target = "versionNumber", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "workflowId", source = "workflowEntity.id")
    @Mapping(target = "workflowType", source = "workflowEntity.type")
    WorkflowVersionEntity workflowConfigEntityToVipWorkflowVersionEntity(WorkflowConfigEntity workflowConfigEntity,
        WorkflowEntity workflowEntity);
}
