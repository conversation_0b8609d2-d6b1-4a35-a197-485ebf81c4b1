package com.isxcode.spark.vip.modules.model.service;

import com.isxcode.spark.api.datasource.dto.ConnectInfo;
import com.isxcode.spark.api.datasource.dto.QueryColumnDto;
import com.isxcode.spark.api.model.ao.DataModelColumnAo;
import com.isxcode.spark.api.model.constant.ColumnFormatStatus;
import com.isxcode.spark.api.model.constant.DataModelStatus;
import com.isxcode.spark.api.model.constant.DataModelType;
import com.isxcode.spark.api.model.req.*;
import com.isxcode.spark.api.model.res.AddColumnFormatRes;
import com.isxcode.spark.api.model.res.ColumnFormatPageRes;
import com.isxcode.spark.api.model.res.DataModelColumnPageRes;
import com.isxcode.spark.api.model.res.DataModelPageRes;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.datasource.entity.DatasourceEntity;
import com.isxcode.spark.modules.datasource.mapper.DatasourceMapper;
import com.isxcode.spark.modules.datasource.service.DatasourceService;
import com.isxcode.spark.modules.datasource.source.DataSourceFactory;
import com.isxcode.spark.modules.datasource.source.Datasource;
import com.isxcode.spark.modules.layer.entity.LayerEntity;
import com.isxcode.spark.modules.model.entity.ColumnFormatEntity;
import com.isxcode.spark.modules.model.entity.DataModelColumnEntity;
import com.isxcode.spark.modules.model.entity.DataModelEntity;
import com.isxcode.spark.modules.model.mapper.ColumnFormatMapper;
import com.isxcode.spark.modules.model.mapper.DataModelMapper;
import com.isxcode.spark.modules.model.repository.ColumnFormatRepository;
import com.isxcode.spark.modules.model.repository.DataModelColumnRepository;
import com.isxcode.spark.modules.model.repository.DataModelRepository;
import com.isxcode.spark.modules.user.service.UserService;
import com.isxcode.spark.vip.modules.layer.service.LayerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.isxcode.spark.common.config.CommonConfig.JPA_TENANT_MODE;
import static com.isxcode.spark.common.config.CommonConfig.TENANT_ID;


@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ModelBizService {

    private final ColumnFormatRepository columnFormatRepository;

    private final DataModelRepository dataModelRepository;

    private final DataModelColumnRepository dataModelColumnRepository;

    private final ModelService modelService;

    private final ColumnFormatMapper columnFormatMapper;

    private final DataModelMapper dataModelMapper;

    private final UserService userService;

    private final LayerService layerService;

    private final DatasourceService datasourceService;

    private final DataSourceFactory dataSourceFactory;

    private final DatasourceMapper datasourceMapper;

    public AddColumnFormatRes addColumnFormat(AddColumnFormatReq addColumnFormatReq) {

        // 判断名称重复
        if (columnFormatRepository.existsByName(addColumnFormatReq.getName())) {
            throw new IsxAppException("字段标准名称重复");
        }

        // 映射字段
        ColumnFormatEntity columnFormat = columnFormatMapper.addColumnFormatReqToColumnFormatEntity(addColumnFormatReq);

        // 可用状态
        columnFormat.setStatus(ColumnFormatStatus.ENABLE);

        // 持久化数据
        columnFormat = columnFormatRepository.save(columnFormat);

        // 返回
        return columnFormatMapper.columnFormatEntityToAddColumnFormatRes(columnFormat);
    }

    public void updateColumnFormat(UpdateColumnFormatReq updateColumnFormatReq) {

        // 判断字段标准是否存在
        ColumnFormatEntity columnFormat = modelService.getColumnFormat(updateColumnFormatReq.getId());

        // 判断名称重复
        columnFormatRepository.findByName(updateColumnFormatReq.getName()).ifPresent(columnFormatMeta -> {
            if (!columnFormatMeta.getId().equals(updateColumnFormatReq.getId())) {
                throw new IsxAppException("字段标准名称重复");
            }
        });

        // 替换值
        columnFormat =
            columnFormatMapper.updateColumnFormatReqToColumnFormatEntity(updateColumnFormatReq, columnFormat);

        // 持久化数据
        columnFormatRepository.save(columnFormat);
    }

    public Page<ColumnFormatPageRes> pageColumnFormat(PageColumnFormatReq pageColumnFormatReq) {

        if (pageColumnFormatReq.getSearchKeyWord() == null) {
            pageColumnFormatReq.setSearchKeyWord("");
        }

        Page<ColumnFormatEntity> columnFormatPage =
            columnFormatRepository.searchAll(pageColumnFormatReq.getSearchKeyWord(),
                PageRequest.of(pageColumnFormatReq.getPage(), pageColumnFormatReq.getPageSize()));

        Page<ColumnFormatPageRes> map =
            columnFormatPage.map(columnFormatMapper::columnFormatEntityToColumnFormatPageRes);

        map.forEach(e -> {
            e.setCreateUsername(userService.getUserName(e.getCreateBy()));
        });

        return map;
    }

    public void deleteColumnFormat(DeleteColumnFormatReq deleteColumnFormatReq) {

        // 判断字段标准是否存在
        ColumnFormatEntity columnFormat = modelService.getColumnFormat(deleteColumnFormatReq.getId());

        // 有模型字段使用，不让删除
        if (dataModelColumnRepository.existsByColumnFormatId(deleteColumnFormatReq.getId())) {
            throw new IsxAppException("有模型使用，无法删除");
        }

        // 删除自己
        columnFormatRepository.delete(columnFormat);
    }

    public void enableColumnFormat(EnableColumnFormatReq enableColumnFormatReq) {

        // 判断字段标准是否存在
        ColumnFormatEntity columnFormat = modelService.getColumnFormat(enableColumnFormatReq.getId());

        columnFormat.setStatus(ColumnFormatStatus.ENABLE);

        // 数据持久化
        columnFormatRepository.save(columnFormat);
    }

    public void disableColumnFormat(DisableColumnFormatReq disableColumnFormatReq) {

        // 判断字段标准是否存在
        ColumnFormatEntity columnFormat = modelService.getColumnFormat(disableColumnFormatReq.getId());

        columnFormat.setStatus(ColumnFormatStatus.DISABLE);

        // 数据持久化
        columnFormatRepository.save(columnFormat);
    }

    public void addDataModel(AddDataModelReq addDataModelReq) {

        // 判断数据分层是否存在
        LayerEntity layer = layerService.getLayer(addDataModelReq.getLayerId());

        // 判断名称重复
        if (dataModelRepository.existsByNameAndLayerId(addDataModelReq.getName(), addDataModelReq.getLayerId())) {
            throw new IsxAppException("数据模型名称重复");
        }

        // 检查表名是否合法
        if (!Strings.isEmpty(layer.getTableRule())) {
            Pattern pattern = Pattern.compile(layer.getTableRule());
            Matcher matcher = pattern.matcher(addDataModelReq.getTableName());
            if (!matcher.matches()) {
                throw new IsxAppException("不满足表名规则:" + layer.getTableRule());
            }
        }

        // 映射字段
        DataModelEntity dataModel = dataModelMapper.addDataModelReqToDataModelEntity(addDataModelReq);

        // 可用状态
        if (DataModelType.LINK_MODEL.equals(dataModel.getModelType())) {
            dataModel.setStatus(DataModelStatus.SUCCESS);
        } else {
            dataModel.setStatus(DataModelStatus.INIT);
        }

        // 持久化数据
        dataModel = dataModelRepository.save(dataModel);

        // 如果是关联表，则自动补齐字段
        if (DataModelType.LINK_MODEL.equals(dataModel.getModelType())) {

            // 获取表字段信息
            DatasourceEntity datasourceEntity = datasourceService.getDatasource(dataModel.getDatasourceId());
            Datasource datasource = dataSourceFactory.getDatasource(dataModel.getDbType());
            try {
                ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
                connectInfo.setDatabase(datasource.parseDbName(datasourceEntity.getJdbcUrl()));
                connectInfo.setTableName(dataModel.getTableName());
                List<QueryColumnDto> queryColumnDtos = datasource.queryColumn(connectInfo);

                List<DataModelColumnEntity> dataModelColumnList = new ArrayList<>();

                // 遍历封装对象
                for (int i = 0; i < queryColumnDtos.size(); i++) {
                    DataModelColumnEntity dataModelMeta = new DataModelColumnEntity();
                    dataModelMeta.setName(Strings.isEmpty(queryColumnDtos.get(i).getColumnComment())
                        ? queryColumnDtos.get(i).getColumnName()
                        : queryColumnDtos.get(i).getColumnComment());
                    dataModelMeta.setColumnName(queryColumnDtos.get(i).getColumnName());
                    dataModelMeta.setLinkColumnType(queryColumnDtos.get(i).getColumnType());
                    dataModelMeta.setColumnIndex(i);
                    dataModelMeta.setModelId(dataModel.getId());
                    dataModelColumnList.add(dataModelMeta);
                }

                // 持久化数据
                dataModelColumnRepository.saveAll(dataModelColumnList);

            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new IsxAppException("关联模型关联异常");
            }
        }
    }

    public void updateDataModel(UpdateDataModelReq updateDataModelReq) {

        // 判断数据模型是否存在
        DataModelEntity dataModel = modelService.getDataModel(updateDataModelReq.getId());

        // 只有新建和失败状态的数据模型可以修改
        if (DataModelStatus.BUILDING.equals(dataModel.getModelType())
            || DataModelStatus.SUCCESS.equals(dataModel.getModelType())) {
            throw new IsxAppException("当前数据模型无法更新");
        }

        // 判断名称重复
        dataModelRepository.findByNameAndLayerId(updateDataModelReq.getName(), updateDataModelReq.getLayerId())
            .ifPresent(dataModelEntity -> {
                if (!dataModelEntity.getId().equals(updateDataModelReq.getId())) {
                    throw new IsxAppException("同分层下数据模型名称重复");
                }
            });

        // 判断数据分层是否存在
        LayerEntity layer = layerService.getLayer(updateDataModelReq.getLayerId());

        // 检查表名是否合法
        if (!Strings.isEmpty(layer.getTableRule())) {
            Pattern pattern = Pattern.compile(layer.getTableRule());
            Matcher matcher = pattern.matcher(updateDataModelReq.getTableName());
            if (!matcher.matches()) {
                throw new IsxAppException("不满足表名规则");
            }
        }

        // 替换值
        DataModelEntity dataModelEntity =
            dataModelMapper.updateDataModelReqToDataModelEntity(updateDataModelReq, dataModel);

        // 持久化数据
        dataModelRepository.save(dataModelEntity);
    }

    public Page<DataModelPageRes> pageDataModel(PageDataModelReq pageDataModelReq) {

        if (pageDataModelReq.getSearchKeyWord() == null) {
            pageDataModelReq.setSearchKeyWord("");
        }

        Page<DataModelEntity> dataModelPage = dataModelRepository.searchAll(pageDataModelReq.getSearchKeyWord(),
            PageRequest.of(pageDataModelReq.getPage(), pageDataModelReq.getPageSize()));

        Page<DataModelPageRes> map = dataModelPage.map(dataModelMapper::dataModelEntityToDataModelPageRes);

        // 翻译字段
        map.getContent().forEach(e -> {
            e.setCreateUsername(userService.getUserName(e.getCreateBy()));
            e.setLayerName(layerService.getLayerFullPathName(e.getLayerId()));
            e.setDatasourceName(datasourceService.getDatasourceName(e.getDatasourceId()));
        });

        // 返回数据
        return map;
    }

    public void deleteDataModel(DeleteDataModelReq deleteDataModelReq) {

        // 判断数据模型是否存在
        DataModelEntity dataModel = modelService.getDataModel(deleteDataModelReq.getId());

        if (DataModelStatus.SUCCESS.equals(dataModel.getModelType())) {
            throw new IsxAppException("模型已经初始化，请先重置");
        }

        // 只有新建的模型可以删除
        if (!DataModelStatus.INIT.equals(dataModel.getStatus())) {
            throw new IsxAppException("请先重置数据模型");
        }

        // 删除字段信息
        List<DataModelColumnEntity> dataModelColumnList = dataModelColumnRepository.findAllByModelId(dataModel.getId());
        dataModelColumnRepository.deleteAll(dataModelColumnList);

        // 删除原始数据
        dataModelRepository.delete(dataModel);
    }

    public void resetDataModel(ResetDataModelReq resetDataModelReq) {

        // 判断数据模型是否存在
        DataModelEntity dataModel = modelService.getDataModel(resetDataModelReq.getId());

        // 构建中午发删除
        if (DataModelStatus.BUILDING.equals(dataModel.getModelType())) {
            throw new IsxAppException("构建中无法重置");
        }

        // 如果是关联模型无法重置
        if (DataModelType.LINK_MODEL.equals(dataModel.getModelType())) {
            throw new IsxAppException("关联模型无法重置");
        }

        // 重置底层表
        if (DataModelStatus.SUCCESS.equals(dataModel.getStatus())) {
            DatasourceEntity datasourceEntity = datasourceService.getDatasource(dataModel.getDatasourceId());
            Datasource datasource = dataSourceFactory.getDatasource(dataModel.getDbType());
            try {
                ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
                datasource.executeSql(connectInfo, "drop table " + dataModel.getTableName());
            } catch (Exception e) {
                dataModel.setBuildLog(e.getMessage());
                dataModel.setStatus(DataModelStatus.FAIL);
                dataModelRepository.save(dataModel);
            }
        }

        // 修改状态
        dataModel.setStatus(DataModelStatus.INIT);
        dataModel.setBuildLog("已重置");
        dataModelRepository.save(dataModel);
    }

    public void addDataModelColumn(AddDataModelColumnReq addDataModelColumnReq) {

        // 判断模型是否存在
        DataModelEntity dataModel = modelService.getDataModel(addDataModelColumnReq.getModelId());

        // 成功或者构建中无法修改
        if (DataModelStatus.BUILDING.equals(dataModel.getModelType())) {
            throw new IsxAppException("构建中无法新建");
        }
        if (DataModelStatus.SUCCESS.equals(dataModel.getModelType())) {
            throw new IsxAppException("模型已经初始化，请先重置");
        }

        // 如果是关联模型无法新建
        if (DataModelType.LINK_MODEL.equals(dataModel.getModelType())) {
            throw new IsxAppException("关联模型无法新建字段");
        }

        // 判断字段标准是否存在
        ColumnFormatEntity columnFormat = modelService.getColumnFormat(addDataModelColumnReq.getColumnFormatId());

        // 判断字段标准是否禁用
        if (ColumnFormatStatus.DISABLE.equals(columnFormat.getStatus())) {
            throw new IsxAppException("该字段标准已禁用");
        }

        // 判断名称是否重复
        if (dataModelColumnRepository.existsByModelIdAndName(dataModel.getId(), addDataModelColumnReq.getName())) {
            throw new IsxAppException("字段名称重复");
        }

        // 判断字段名是否规范
        if (!Strings.isEmpty(columnFormat.getColumnRule())) {
            Pattern pattern = Pattern.compile(columnFormat.getColumnRule());
            Matcher matcher = pattern.matcher(addDataModelColumnReq.getColumnName());
            if (!matcher.matches()) {
                throw new IsxAppException("不满足字段名规则:" + columnFormat.getColumnRule());
            }
        }

        // 映射字段
        DataModelColumnEntity dataModelColumn =
            dataModelMapper.addDataModelColumnReqToDataModelColumnEntity(addDataModelColumnReq);

        // 获取字段顺序
        long maxIndex = dataModelColumnRepository.countByModelId(dataModel.getId());
        dataModelColumn.setColumnIndex(Integer.valueOf(String.valueOf(maxIndex)));

        // 持久化数据
        dataModelColumnRepository.save(dataModelColumn);
    }

    public void updateDataModelColumn(UpdateDataModelColumnReq updateDataModelColumnReq) {

        // 判断字段是否存在
        DataModelColumnEntity dataModelColumn = modelService.getDataModelColumn(updateDataModelColumnReq.getId());

        // 判断模型是否存在
        DataModelEntity dataModel = modelService.getDataModel(dataModelColumn.getModelId());

        // 关联模型无法编辑
        if (DataModelType.LINK_MODEL.equals(dataModel.getModelType())) {
            throw new IsxAppException("关联模型无法编辑字段");
        }

        if (DataModelStatus.SUCCESS.equals(dataModel.getStatus())) {
            throw new IsxAppException("模型已经初始化，请先重置");
        }

        // 成功或者构建中无法修改
        if (DataModelStatus.BUILDING.equals(dataModel.getModelType())) {
            throw new IsxAppException("构建中无法新建");
        }

        // 判断字段标准是否存在
        ColumnFormatEntity columnFormat = modelService.getColumnFormat(updateDataModelColumnReq.getColumnFormatId());

        // 判断字段标准是否禁用
        if (ColumnFormatStatus.DISABLE.equals(columnFormat.getStatus())) {
            throw new IsxAppException("该字段标准已禁用");
        }

        // 判断名称重复
        dataModelColumnRepository.findByNameAndModelId(updateDataModelColumnReq.getName(), dataModel.getId())
            .ifPresent(dataModelColumnEntity -> {
                if (!dataModelColumnEntity.getId().equals(updateDataModelColumnReq.getId())) {
                    throw new IsxAppException("数据模型名称重复");
                }
            });

        // 判断字段名是否规范
        if (!Strings.isEmpty(columnFormat.getColumnRule())) {
            Pattern pattern = Pattern.compile(columnFormat.getColumnRule());
            Matcher matcher = pattern.matcher(updateDataModelColumnReq.getColumnName());
            if (!matcher.matches()) {
                throw new IsxAppException("不满足字段名规则:" + columnFormat.getColumnRule());
            }
        }

        // 映射字段
        dataModelColumn =
            dataModelMapper.updateDataModelColumnReqToDataModelColumnEntity(updateDataModelColumnReq, dataModelColumn);

        // 持久化数据
        dataModelColumnRepository.save(dataModelColumn);
    }

    public void updateDataModelColumnIndex(UpdateDataModelColumnIndexReq updateDataModelColumnIndexReq) {

        DataModelEntity dataMode = modelService.getDataModel(updateDataModelColumnIndexReq.getModelId());
        if (DataModelStatus.SUCCESS.equals(dataMode.getStatus())) {
            throw new IsxAppException("模型已经初始化，请先重置");
        }

        // 关联模型无法编辑字段
        if (DataModelType.LINK_MODEL.equals(dataMode.getModelType())) {
            throw new IsxAppException("关联模型无法编辑字段");
        }

        for (int i = 0; i < updateDataModelColumnIndexReq.getDataModelColumnIdList().size(); i++) {
            DataModelColumnEntity dataModelColumn =
                modelService.getDataModelColumn(updateDataModelColumnIndexReq.getDataModelColumnIdList().get(i));
            dataModelColumn.setColumnIndex(i);
            dataModelColumnRepository.save(dataModelColumn);
        }
    }

    public Page<DataModelColumnPageRes> pageDataModelColumn(PageDataModelColumnReq pageDataModelColumnReq) {

        // 查询数据模型
        DataModelEntity dataModel = modelService.getDataModel(pageDataModelColumnReq.getModelId());

        if (pageDataModelColumnReq.getSearchKeyWord() == null) {
            pageDataModelColumnReq.setSearchKeyWord("");
        }

        JPA_TENANT_MODE.set(false);
        Page<DataModelColumnAo> dataModelColumnPage = dataModelColumnRepository.searchAll(TENANT_ID.get(),
            pageDataModelColumnReq.getSearchKeyWord(), pageDataModelColumnReq.getModelId(),
            PageRequest.of(pageDataModelColumnReq.getPage(), pageDataModelColumnReq.getPageSize()));
        JPA_TENANT_MODE.set(true);

        if (DataModelType.LINK_MODEL.equals(dataModel.getModelType())) {
            Page<DataModelColumnPageRes> map =
                dataModelColumnPage.map(dataModelMapper::dataModelColumnAoToDataModelColumnPageRes);
            map.getContent()
                .forEach(dataModelColumn -> dataModelColumn.setColumnType(dataModelColumn.getLinkColumnType()));
            return map;
        } else {
            return dataModelColumnPage.map(dataModelMapper::dataModelColumnAoToDataModelColumnPageRes);
        }
    }

    public void deleteDataModelColumn(DeleteColumnFormatReq deleteColumnFormatReq) {

        // 判断字段是否存在
        DataModelColumnEntity dataModelColumn = modelService.getDataModelColumn(deleteColumnFormatReq.getId());

        // 判断模型是否存在
        DataModelEntity dataModel = modelService.getDataModel(dataModelColumn.getModelId());

        // 如果是关联模型无法删除
        if (DataModelType.LINK_MODEL.equals(dataModel.getModelType())) {
            throw new IsxAppException("关联模型无法删除字段");
        }

        // 成功或者构建中无法修改
        if (DataModelStatus.BUILDING.equals(dataModel.getModelType())) {
            throw new IsxAppException("构建中无法删除");
        }
        if (DataModelStatus.SUCCESS.equals(dataModel.getModelType())) {
            throw new IsxAppException("请先重置数据模型");
        }

        // 删除模型字段
        dataModelColumnRepository.delete(dataModelColumn);
    }

    public Page<DataModelPageRes> pageDataModelByLayer(PageDataModelByLayerReq pageDataModelByLayerReq) {

        if (pageDataModelByLayerReq.getSearchKeyWord() == null) {
            pageDataModelByLayerReq.setSearchKeyWord("");
        }

        Page<DataModelEntity> dataModelPage = dataModelRepository.searchAllByLayerId(
            pageDataModelByLayerReq.getSearchKeyWord(), pageDataModelByLayerReq.getLayerId(),
            PageRequest.of(pageDataModelByLayerReq.getPage(), pageDataModelByLayerReq.getPageSize()));

        Page<DataModelPageRes> map = dataModelPage.map(dataModelMapper::dataModelEntityToDataModelPageRes);

        // 翻译字段
        map.getContent().forEach(e -> {
            e.setCreateUsername(userService.getUserName(e.getCreateBy()));
            e.setLayerName(layerService.getLayerFullPathName(e.getLayerId()));
            e.setDatasourceName(datasourceService.getDatasourceName(e.getDatasourceId()));
        });

        // 返回数据
        return map;
    }

    public void copyDataModel(CopyDataModelReq copyDataModelReq) {

        // 判断数据模型是否存在
        DataModelEntity dataModel = modelService.getDataModel(copyDataModelReq.getModelId());

        // 关联模型不支持复制
        if (DataModelType.LINK_MODEL.equals(dataModel.getModelType())) {
            throw new IsxAppException("关联模型暂不支持复制");
        }

        // 判断数据分层是否存在
        LayerEntity layer = layerService.getLayer(copyDataModelReq.getLayerId());

        // 判断名称是否重复
        if (dataModelRepository.existsByName(copyDataModelReq.getName())) {
            throw new IsxAppException("数据模型名称重复");
        }

        // 检查表名是否合法
        if (!Strings.isEmpty(layer.getTableRule())) {
            Pattern pattern = Pattern.compile(layer.getTableRule());
            Matcher matcher = pattern.matcher(copyDataModelReq.getTableName());
            if (!matcher.matches()) {
                throw new IsxAppException("不满足表名规则");
            }
        }

        DataModelEntity dataModelNew = dataModelMapper.copyDataModelReqToDataModelEntity(copyDataModelReq, dataModel);
        dataModelNew.setStatus(DataModelStatus.INIT);
        dataModelNew = dataModelRepository.save(dataModelNew);

        // 查询字段信息
        String newModelId = dataModelNew.getId();
        dataModelColumnRepository.findAllByModelId(copyDataModelReq.getModelId()).forEach(dataModelColumnMeta -> {
            DataModelColumnEntity dataModelColumnNew =
                dataModelMapper.copyDataModelReqToDataModelColumnEntity(dataModelColumnMeta);
            dataModelColumnNew.setModelId(newModelId);
            dataModelColumnRepository.save(dataModelColumnNew);
        });
    }

    public void buildDataModel(BuildDataModelReq buildDataModelReq) {

        DataModelEntity dataModel = modelService.getDataModel(buildDataModelReq.getModelId());

        // 关联模型不支持构建
        if (DataModelType.LINK_MODEL.equals(dataModel.getModelType())) {
            throw new IsxAppException("关联模型暂不支持构建");
        }

        // 已经成功的模型不能再构建，需要先重置
        if (DataModelStatus.SUCCESS.equals(dataModel.getStatus())) {
            throw new IsxAppException("模型已经初始化，请先重置");
        }

        // 查询模型字段
        JPA_TENANT_MODE.set(false);
        List<DataModelColumnAo> modelColumnList = dataModelColumnRepository
            .searchAll(TENANT_ID.get(), "", dataModel.getId(), PageRequest.of(0, 999)).getContent();
        JPA_TENANT_MODE.set(true);

        if (modelColumnList.isEmpty()) {
            throw new IsxAppException("模型列不能为空");
        }

        // 获取表字段信息
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(dataModel.getDatasourceId());
        Datasource datasource = dataSourceFactory.getDatasource(dataModel.getDbType());
        try {
            ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
            String sql = datasource.generateDataModelSql(connectInfo, modelColumnList, dataModel);
            datasource.executeSql(connectInfo, sql);

            // 修改状态日志
            dataModel.setStatus(DataModelStatus.SUCCESS);
            dataModel.setBuildLog("构建成功");
            dataModelRepository.save(dataModel);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 修改状态日志
            dataModel.setStatus(DataModelStatus.FAIL);
            dataModel.setBuildLog("构建失败:" + e.getMessage());
            if (e instanceof IsxAppException) {
                dataModel.setBuildLog("构建失败:" + ((IsxAppException) e).getMsg());
            }
            dataModelRepository.save(dataModel);
        }
    }
}
