package com.isxcode.spark.vip.modules.workflow.instance;


import com.isxcode.spark.api.instance.constants.InstanceStatus;
import com.isxcode.spark.api.instance.req.DeleteWorkflowInstanceReq;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.work.repository.WorkInstanceRepository;
import com.isxcode.spark.modules.workflow.entity.WorkflowInstanceEntity;
import com.isxcode.spark.modules.workflow.repository.WorkflowInstanceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class WorkflowInstanceBizService {

    private final WorkflowInstanceRepository workflowInstanceRepository;

    private final WorkflowInstanceMapper workflowInstanceMapper;

    private final WorkInstanceRepository workInstanceRepository;

    @Transactional
    public void deleteWorkflowInstance(DeleteWorkflowInstanceReq deleteWorkflowInstanceReq) {

        WorkflowInstanceEntity workflowInstanceEntity =
            workflowInstanceRepository.findById(deleteWorkflowInstanceReq.getWorkflowInstanceId())
                .orElseThrow(() -> new IsxAppException("作业流实例不存在"));

        if (InstanceStatus.RUNNING.equals(workflowInstanceEntity.getStatus())
            || InstanceStatus.ABORTING.equals(workflowInstanceEntity.getStatus())) {
            throw new IsxAppException("运行中的作业流实例不能删除");
        }

        workInstanceRepository.deleteAllByWorkflowInstanceId(deleteWorkflowInstanceReq.getWorkflowInstanceId());

        workflowInstanceRepository.deleteById(deleteWorkflowInstanceReq.getWorkflowInstanceId());
    }
}
